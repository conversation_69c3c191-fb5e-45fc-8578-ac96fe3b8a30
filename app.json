{"pages": ["pages/home/<USER>", "pages/mine/index", "pages/order/index", "pages/add_menu/index", "pages/statistics/index", "pages/today_order/index", "pages/login/index", "pages/message/index", "pages/family_message/index", "pages/notification_center/index", "pages/history_menu/index", "pages/detail/index", "pages/user_connection/index"], "requiredPrivateInfos": ["getLocation"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#FFFFFF", "navigationBarTitleText": "楠楠家厨", "navigationBarTextStyle": "black", "backgroundColor": "#FFFFFF", "disableScroll": false}, "tabBar": {"color": "#6B7280", "selectedColor": "#3B82F6", "backgroundColor": "#FFFFFF", "borderStyle": "white", "list": [{"pagePath": "pages/home/<USER>", "text": "首页", "iconPath": "assets/image/shouye_huaban.png", "selectedIconPath": "assets/image/shouye_huaban1.png"}, {"pagePath": "pages/order/index", "text": "点菜", "iconPath": "assets/image/diancaidanicon.png", "selectedIconPath": "assets/image/diancaidanicon1.png"}, {"pagePath": "pages/add_menu/index", "text": "新增菜单", "iconPath": "assets/image/xinzeng.png", "selectedIconPath": "assets/image/xinzeng1.png"}, {"pagePath": "pages/statistics/index", "text": "统计", "iconPath": "assets/image/tongji.png", "selectedIconPath": "assets/image/tongji1.png"}, {"pagePath": "pages/mine/index", "text": "我的", "iconPath": "assets/image/wode.png", "selectedIconPath": "assets/image/wode1.png"}]}, "darkmode": true, "style": "v2", "permission": {}, "usingComponents": {"van-toast": "@vant/weapp/lib/toast/index", "van-loading": "@vant/weapp/lib/loading/index", "van-dialog": "@vant/weapp/lib/dialog/index", "van-notify": "@vant/weapp/lib/notify/index", "van-overlay": "@vant/weapp/lib/overlay/index", "van-button": "@vant/weapp/lib/button/index", "van-cell": "@vant/weapp/lib/cell/index", "van-cell-group": "@vant/weapp/lib/cell-group/index", "van-field": "@vant/weapp/lib/field/index", "van-popup": "@vant/weapp/lib/popup/index", "van-picker": "@vant/weapp/lib/picker/index", "van-datetime-picker": "@vant/weapp/lib/datetime-picker/index", "van-action-sheet": "@vant/weapp/lib/action-sheet/index", "van-swipe-cell": "@vant/weapp/lib/swipe-cell/index", "van-tag": "@vant/weapp/lib/tag/index", "van-empty": "@vant/weapp/lib/empty/index", "van-divider": "@vant/weapp/lib/divider/index", "van-icon": "@vant/weapp/lib/icon/index"}, "sitemapLocation": "sitemap.json"}