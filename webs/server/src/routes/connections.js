const express = require('express');
const router = express.Router();
const {auth} = require('../middlewares/auth');
const {
  getAvailableUsers,
  sendConnectionRequest,
  respondToConnection,
  getMyConnections,
  removeConnection
} = require('../controllers/connectionController');

// 所有路由都需要认证
router.use(auth);

/**
 * @route GET /api/connections/users
 * @desc 获取可关联的用户列表
 * @access Private
 */
router.get('/users', getAvailableUsers);

/**
 * @route POST /api/connections/request
 * @desc 发送关联申请
 * @access Private
 */
router.post('/request', sendConnectionRequest);

/**
 * @route PUT /api/connections/:id/respond
 * @desc 处理关联申请（同意/拒绝）
 * @access Private
 */
router.put('/:id/respond', respondToConnection);

/**
 * @route GET /api/connections/my
 * @desc 获取我的关联列表
 * @access Private
 */
router.get('/my', getMyConnections);

/**
 * @route DELETE /api/connections/:id
 * @desc 取消关联关系
 * @access Private
 */
router.delete('/:id', removeConnection);

module.exports = router;
