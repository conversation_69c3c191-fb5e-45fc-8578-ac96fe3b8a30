const express = require('express');
const router = express.Router();
const {auth} = require('../middlewares/auth');
const {
  getMyGroups,
  createGroup,
  updateGroup,
  deleteGroup,
  getGroupMembers
} = require('../controllers/userGroupController');

// 所有路由都需要认证
router.use(auth);

/**
 * @route GET /api/user-groups
 * @desc 获取我的分组列表
 * @access Private
 */
router.get('/', getMyGroups);

/**
 * @route POST /api/user-groups
 * @desc 创建分组
 * @access Private
 */
router.post('/', createGroup);

/**
 * @route PUT /api/user-groups/:id
 * @desc 更新分组
 * @access Private
 */
router.put('/:id', updateGroup);

/**
 * @route DELETE /api/user-groups/:id
 * @desc 删除分组
 * @access Private
 */
router.delete('/:id', deleteGroup);

/**
 * @route GET /api/user-groups/:id/members
 * @desc 获取分组成员列表
 * @access Private
 */
router.get('/:id/members', getGroupMembers);

module.exports = router;
