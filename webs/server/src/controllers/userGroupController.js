const prisma = require('../utils/prisma');
const {success, error} = require('../utils/response');

/**
 * 获取我的分组列表
 * @route GET /api/user-groups
 */
const getMyGroups = async (req, res) => {
  try {
    const userId = req.user.id;

    const groups = await prisma.userGroup.findMany({
      where: {
        ownerId: userId
      },
      include: {
        _count: {
          select: {
            connections: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // 格式化返回数据
    const formattedGroups = groups.map(group => ({
      id: group.id,
      name: group.name,
      description: group.description,
      color: group.color,
      memberCount: group._count.connections,
      createdAt: group.createdAt,
      updatedAt: group.updatedAt
    }));

    return success(res, formattedGroups);
  } catch (err) {
    console.error('Get my groups error:', err);
    return error(res, 'Failed to get groups', 500);
  }
};

/**
 * 创建分组
 * @route POST /api/user-groups
 */
const createGroup = async (req, res) => {
  try {
    const userId = req.user.id;
    const {name, description, color} = req.body;

    if (!name || name.trim().length === 0) {
      return error(res, 'Group name is required', 400);
    }

    if (name.length > 20) {
      return error(res, 'Group name cannot exceed 20 characters', 400);
    }

    // 检查是否已存在同名分组
    const existingGroup = await prisma.userGroup.findFirst({
      where: {
        ownerId: userId,
        name: name.trim()
      }
    });

    if (existingGroup) {
      return error(res, 'Group name already exists', 409);
    }

    const group = await prisma.userGroup.create({
      data: {
        name: name.trim(),
        description: description?.trim() || null,
        color: color || '#6366F1',
        ownerId: userId
      },
      include: {
        _count: {
          select: {
            connections: true
          }
        }
      }
    });

    const formattedGroup = {
      id: group.id,
      name: group.name,
      description: group.description,
      color: group.color,
      memberCount: group._count.connections,
      createdAt: group.createdAt,
      updatedAt: group.updatedAt
    };

    return success(res, formattedGroup, 'Group created successfully', 201);
  } catch (err) {
    console.error('Create group error:', err);
    return error(res, 'Failed to create group', 500);
  }
};

/**
 * 更新分组
 * @route PUT /api/user-groups/:id
 */
const updateGroup = async (req, res) => {
  try {
    const {id} = req.params;
    const userId = req.user.id;
    const {name, description, color} = req.body;

    // 检查分组是否存在且属于当前用户
    const existingGroup = await prisma.userGroup.findFirst({
      where: {
        id,
        ownerId: userId
      }
    });

    if (!existingGroup) {
      return error(res, 'Group not found', 404);
    }

    if (name && name.trim().length === 0) {
      return error(res, 'Group name cannot be empty', 400);
    }

    if (name && name.length > 20) {
      return error(res, 'Group name cannot exceed 20 characters', 400);
    }

    // 如果要更新名称，检查是否与其他分组重名
    if (name && name.trim() !== existingGroup.name) {
      const duplicateGroup = await prisma.userGroup.findFirst({
        where: {
          ownerId: userId,
          name: name.trim(),
          id: {not: id}
        }
      });

      if (duplicateGroup) {
        return error(res, 'Group name already exists', 409);
      }
    }

    const updateData = {};
    if (name !== undefined) updateData.name = name.trim();
    if (description !== undefined) updateData.description = description?.trim() || null;
    if (color !== undefined) updateData.color = color || '#6366F1';

    const updatedGroup = await prisma.userGroup.update({
      where: {id},
      data: updateData,
      include: {
        _count: {
          select: {
            connections: true
          }
        }
      }
    });

    const formattedGroup = {
      id: updatedGroup.id,
      name: updatedGroup.name,
      description: updatedGroup.description,
      color: updatedGroup.color,
      memberCount: updatedGroup._count.connections,
      createdAt: updatedGroup.createdAt,
      updatedAt: updatedGroup.updatedAt
    };

    return success(res, formattedGroup, 'Group updated successfully');
  } catch (err) {
    console.error('Update group error:', err);
    return error(res, 'Failed to update group', 500);
  }
};

/**
 * 删除分组
 * @route DELETE /api/user-groups/:id
 */
const deleteGroup = async (req, res) => {
  try {
    const {id} = req.params;
    const userId = req.user.id;

    // 检查分组是否存在且属于当前用户
    const existingGroup = await prisma.userGroup.findFirst({
      where: {
        id,
        ownerId: userId
      },
      include: {
        _count: {
          select: {
            connections: true
          }
        }
      }
    });

    if (!existingGroup) {
      return error(res, 'Group not found', 404);
    }

    // 删除分组前，将该分组下的关联关系的groupId设为null
    await prisma.userConnection.updateMany({
      where: {
        groupId: id
      },
      data: {
        groupId: null
      }
    });

    // 删除分组
    await prisma.userGroup.delete({
      where: {id}
    });

    return success(res, null, 'Group deleted successfully');
  } catch (err) {
    console.error('Delete group error:', err);
    return error(res, 'Failed to delete group', 500);
  }
};

/**
 * 获取分组详情（包含成员列表）
 * @route GET /api/user-groups/:id/members
 */
const getGroupMembers = async (req, res) => {
  try {
    const {id} = req.params;
    const userId = req.user.id;

    // 检查分组是否存在且属于当前用户
    const group = await prisma.userGroup.findFirst({
      where: {
        id,
        ownerId: userId
      },
      include: {
        connections: {
          where: {
            status: 'accepted'
          },
          include: {
            sender: {
              select: {id: true, name: true, avatar: true, phone: true, role: true}
            },
            receiver: {
              select: {id: true, name: true, avatar: true, phone: true, role: true}
            }
          }
        }
      }
    });

    if (!group) {
      return error(res, 'Group not found', 404);
    }

    // 格式化成员列表
    const members = group.connections.map(conn => {
      const isCurrentUserSender = conn.senderId === userId;
      const connectedUser = isCurrentUserSender ? conn.receiver : conn.sender;
      
      return {
        connectionId: conn.id,
        user: connectedUser,
        remark: conn.remark,
        joinedAt: conn.updatedAt
      };
    });

    const groupData = {
      id: group.id,
      name: group.name,
      description: group.description,
      color: group.color,
      memberCount: members.length,
      members,
      createdAt: group.createdAt,
      updatedAt: group.updatedAt
    };

    return success(res, groupData);
  } catch (err) {
    console.error('Get group members error:', err);
    return error(res, 'Failed to get group members', 500);
  }
};

module.exports = {
  getMyGroups,
  createGroup,
  updateGroup,
  deleteGroup,
  getGroupMembers
};
