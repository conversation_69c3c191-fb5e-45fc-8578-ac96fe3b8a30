const prisma = require('../utils/prisma');
const {success, error} = require('../utils/response');
const notificationService = require('../services/notificationService');

/**
 * 获取用户列表（用于选择关联对象）
 * @route GET /api/connections/users
 */
const getAvailableUsers = async (req, res) => {
  try {
    const currentUserId = req.user.id;
    const {search = '', page = 1, size = 20} = req.query;

    const pageNum = parseInt(page);
    const pageSize = parseInt(size);
    const skip = (pageNum - 1) * pageSize;

    // 构建查询条件
    const where = {
      id: {not: currentUserId} // 排除当前用户
    };

    if (search) {
      where.OR = [
        {name: {contains: search, mode: 'insensitive'}},
        {phone: {contains: search, mode: 'insensitive'}}
      ];
    }

    // 获取总数
    const total = await prisma.user.count({where});

    // 获取用户列表
    const users = await prisma.user.findMany({
      where,
      select: {
        id: true,
        name: true,
        phone: true,
        avatar: true,
        role: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: pageSize
    });

    // 获取当前用户与这些用户的关联状态
    const userIds = users.map(user => user.id);
    const connections = await prisma.userConnection.findMany({
      where: {
        OR: [
          {senderId: currentUserId, receiverId: {in: userIds}},
          {receiverId: currentUserId, senderId: {in: userIds}}
        ]
      }
    });

    // 构建关联状态映射
    const connectionMap = {};
    connections.forEach(conn => {
      const otherUserId =
        conn.senderId === currentUserId ? conn.receiverId : conn.senderId;
      connectionMap[otherUserId] = {
        status: conn.status,
        isSender: conn.senderId === currentUserId,
        connectionId: conn.id
      };
    });

    // 格式化返回数据
    const formattedUsers = users.map(user => ({
      ...user,
      connectionStatus: connectionMap[user.id]?.status || 'none',
      isSender: connectionMap[user.id]?.isSender || false,
      connectionId: connectionMap[user.id]?.connectionId || null
    }));

    return success(res, {
      list: formattedUsers,
      total,
      page: pageNum,
      size: pageSize,
      totalPages: Math.ceil(total / pageSize)
    });
  } catch (err) {
    console.error('Get available users error:', err);
    return error(res, 'Failed to get available users', 500);
  }
};

/**
 * 发送关联申请
 * @route POST /api/connections/request
 */
const sendConnectionRequest = async (req, res) => {
  try {
    const senderId = req.user.id;
    const {receiverId, message} = req.body;

    if (!receiverId) {
      return error(res, 'Receiver ID is required', 400);
    }

    if (senderId === receiverId) {
      return error(res, 'Cannot connect to yourself', 400);
    }

    // 检查接收者是否存在
    const receiver = await prisma.user.findUnique({
      where: {id: receiverId}
    });

    if (!receiver) {
      return error(res, 'Receiver not found', 404);
    }

    // 检查是否已存在关联关系
    const existingConnection = await prisma.userConnection.findFirst({
      where: {
        OR: [
          {senderId, receiverId},
          {senderId: receiverId, receiverId: senderId}
        ]
      }
    });

    if (existingConnection) {
      return error(res, 'Connection already exists', 409);
    }

    // 创建关联申请
    const connection = await prisma.userConnection.create({
      data: {
        senderId,
        receiverId,
        message: message || '请求添加为关联用户',
        status: 'pending'
      },
      include: {
        sender: {
          select: {id: true, name: true, avatar: true}
        },
        receiver: {
          select: {id: true, name: true, avatar: true}
        }
      }
    });

    // 发送通知给接收者
    await notificationService.sendNotification({
      userId: receiverId,
      senderId,
      type: 'connection_request',
      title: '新的关联申请',
      content: `${req.user.name} 请求添加您为关联用户`,
      data: JSON.stringify({
        connectionId: connection.id,
        senderName: req.user.name,
        message: message || '请求添加为关联用户'
      })
    });

    return success(
      res,
      connection,
      'Connection request sent successfully',
      201
    );
  } catch (err) {
    console.error('Send connection request error:', err);
    return error(res, 'Failed to send connection request', 500);
  }
};

/**
 * 处理关联申请（同意/拒绝）
 * @route PUT /api/connections/:id/respond
 */
const respondToConnection = async (req, res) => {
  try {
    const {id} = req.params;
    const {action} = req.body; // 'accept' or 'reject'
    const currentUserId = req.user.id;

    if (!['accept', 'reject'].includes(action)) {
      return error(res, 'Invalid action. Must be "accept" or "reject"', 400);
    }

    // 查找关联申请
    const connection = await prisma.userConnection.findUnique({
      where: {id},
      include: {
        sender: {
          select: {id: true, name: true, avatar: true}
        },
        receiver: {
          select: {id: true, name: true, avatar: true}
        }
      }
    });

    if (!connection) {
      return error(res, 'Connection not found', 404);
    }

    // 检查权限（只有接收者可以处理申请）
    if (connection.receiverId !== currentUserId) {
      return error(res, 'Permission denied', 403);
    }

    if (connection.status !== 'pending') {
      return error(res, 'Connection already processed', 400);
    }

    // 更新关联状态
    const updatedConnection = await prisma.userConnection.update({
      where: {id},
      data: {
        status: action === 'accept' ? 'accepted' : 'rejected'
      },
      include: {
        sender: {
          select: {id: true, name: true, avatar: true}
        },
        receiver: {
          select: {id: true, name: true, avatar: true}
        }
      }
    });

    // 发送通知给申请者
    const notificationContent =
      action === 'accept'
        ? `${req.user.name} 同意了您的关联申请`
        : `${req.user.name} 拒绝了您的关联申请`;

    await notificationService.sendNotification({
      userId: connection.senderId,
      senderId: currentUserId,
      type: 'connection_response',
      title: '关联申请回复',
      content: notificationContent,
      data: JSON.stringify({
        connectionId: connection.id,
        action,
        responderName: req.user.name
      })
    });

    return success(
      res,
      updatedConnection,
      `Connection ${action}ed successfully`
    );
  } catch (err) {
    console.error('Respond to connection error:', err);
    return error(res, 'Failed to respond to connection', 500);
  }
};

/**
 * 获取我的关联列表
 * @route GET /api/connections/my
 */
const getMyConnections = async (req, res) => {
  try {
    const currentUserId = req.user.id;
    const {status = 'accepted', groupId} = req.query;

    const whereCondition = {
      OR: [{senderId: currentUserId}, {receiverId: currentUserId}],
      status
    };

    // 如果指定了分组ID，则只查询该分组的关联
    if (groupId) {
      whereCondition.groupId = groupId;
    }

    const connections = await prisma.userConnection.findMany({
      where: whereCondition,
      include: {
        sender: {
          select: {id: true, name: true, avatar: true, phone: true, role: true}
        },
        receiver: {
          select: {id: true, name: true, avatar: true, phone: true, role: true}
        },
        group: {
          select: {id: true, name: true, color: true}
        }
      },
      orderBy: {
        updatedAt: 'desc'
      }
    });

    // 格式化返回数据
    const formattedConnections = connections.map(conn => {
      const isCurrentUserSender = conn.senderId === currentUserId;
      const connectedUser = isCurrentUserSender ? conn.receiver : conn.sender;

      return {
        id: conn.id,
        status: conn.status,
        message: conn.message,
        remark: conn.remark,
        group: conn.group,
        createdAt: conn.createdAt,
        updatedAt: conn.updatedAt,
        isSender: isCurrentUserSender,
        connectedUser
      };
    });

    return success(res, formattedConnections);
  } catch (err) {
    console.error('Get my connections error:', err);
    return error(res, 'Failed to get connections', 500);
  }
};

/**
 * 取消关联关系
 * @route DELETE /api/connections/:id
 */
const removeConnection = async (req, res) => {
  try {
    const {id} = req.params;
    const currentUserId = req.user.id;

    // 查找关联关系
    const connection = await prisma.userConnection.findUnique({
      where: {id},
      include: {
        sender: {
          select: {id: true, name: true}
        },
        receiver: {
          select: {id: true, name: true}
        }
      }
    });

    if (!connection) {
      return error(res, 'Connection not found', 404);
    }

    // 检查权限（只有关联的双方可以删除）
    if (
      connection.senderId !== currentUserId &&
      connection.receiverId !== currentUserId
    ) {
      return error(res, 'Permission denied', 403);
    }

    // 删除关联关系
    await prisma.userConnection.delete({
      where: {id}
    });

    // 通知对方
    const otherUserId =
      connection.senderId === currentUserId
        ? connection.receiverId
        : connection.senderId;
    await notificationService.sendNotification({
      userId: otherUserId,
      senderId: currentUserId,
      type: 'connection_removed',
      title: '关联关系已解除',
      content: `${req.user.name} 解除了与您的关联关系`,
      data: JSON.stringify({
        removedBy: req.user.name
      })
    });

    return success(res, null, 'Connection removed successfully');
  } catch (err) {
    console.error('Remove connection error:', err);
    return error(res, 'Failed to remove connection', 500);
  }
};

/**
 * 更新关联备注和分组
 * @route PUT /api/connections/:id/update
 */
const updateConnection = async (req, res) => {
  try {
    const {id} = req.params;
    const currentUserId = req.user.id;
    const {remark, groupId} = req.body;

    // 查找关联关系
    const connection = await prisma.userConnection.findUnique({
      where: {id},
      include: {
        sender: {
          select: {id: true, name: true}
        },
        receiver: {
          select: {id: true, name: true}
        }
      }
    });

    if (!connection) {
      return error(res, 'Connection not found', 404);
    }

    // 检查权限（只有关联的双方可以更新）
    if (
      connection.senderId !== currentUserId &&
      connection.receiverId !== currentUserId
    ) {
      return error(res, 'Permission denied', 403);
    }

    // 如果指定了分组ID，检查分组是否存在且属于当前用户
    if (groupId) {
      const group = await prisma.userGroup.findFirst({
        where: {
          id: groupId,
          ownerId: currentUserId
        }
      });

      if (!group) {
        return error(res, 'Group not found or access denied', 404);
      }
    }

    const updateData = {};
    if (remark !== undefined) updateData.remark = remark?.trim() || null;
    if (groupId !== undefined) updateData.groupId = groupId || null;

    const updatedConnection = await prisma.userConnection.update({
      where: {id},
      data: updateData,
      include: {
        sender: {
          select: {id: true, name: true, avatar: true, phone: true, role: true}
        },
        receiver: {
          select: {id: true, name: true, avatar: true, phone: true, role: true}
        },
        group: {
          select: {id: true, name: true, color: true}
        }
      }
    });

    // 格式化返回数据
    const isCurrentUserSender = updatedConnection.senderId === currentUserId;
    const connectedUser = isCurrentUserSender
      ? updatedConnection.receiver
      : updatedConnection.sender;

    const formattedConnection = {
      id: updatedConnection.id,
      status: updatedConnection.status,
      message: updatedConnection.message,
      remark: updatedConnection.remark,
      group: updatedConnection.group,
      createdAt: updatedConnection.createdAt,
      updatedAt: updatedConnection.updatedAt,
      isSender: isCurrentUserSender,
      connectedUser
    };

    return success(res, formattedConnection, 'Connection updated successfully');
  } catch (err) {
    console.error('Update connection error:', err);
    return error(res, 'Failed to update connection', 500);
  }
};

module.exports = {
  getAvailableUsers,
  sendConnectionRequest,
  respondToConnection,
  getMyConnections,
  removeConnection,
  updateConnection
};
