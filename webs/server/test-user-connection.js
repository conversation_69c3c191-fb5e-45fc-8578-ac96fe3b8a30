const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

// 测试用户数据
const testUsers = [
  {
    name: '张三',
    phone: '13800138001',
    password: '123456',
    role: 'user'
  },
  {
    name: '李四',
    phone: '13800138002',
    password: '123456',
    role: 'user'
  },
  {
    name: '王五',
    phone: '13800138003',
    password: '123456',
    role: 'family_head'
  }
];

let userTokens = {};
let userIds = {};

/**
 * 注册用户
 */
async function registerUsers() {
  console.log('\n🔐 注册测试用户...');
  
  for (const user of testUsers) {
    try {
      const response = await axios.post(`${BASE_URL}/auth/register`, user);
      console.log(`✅ 用户 ${user.name} 注册成功`);
    } catch (error) {
      if (error.response?.status === 409) {
        console.log(`⚠️  用户 ${user.name} 已存在，跳过注册`);
      } else {
        console.error(`❌ 用户 ${user.name} 注册失败:`, error.response?.data?.message || error.message);
      }
    }
  }
}

/**
 * 用户登录
 */
async function loginUsers() {
  console.log('\n🔑 用户登录...');
  
  for (const user of testUsers) {
    try {
      const response = await axios.post(`${BASE_URL}/auth/login`, {
        phone: user.phone,
        password: user.password
      });
      
      const { token, user: userData } = response.data.data;
      userTokens[user.name] = token;
      userIds[user.name] = userData.id;
      
      console.log(`✅ 用户 ${user.name} 登录成功`);
    } catch (error) {
      console.error(`❌ 用户 ${user.name} 登录失败:`, error.response?.data?.message || error.message);
    }
  }
}

/**
 * 获取可关联用户列表
 */
async function getAvailableUsers(userName) {
  console.log(`\n👥 ${userName} 获取可关联用户列表...`);
  
  try {
    const response = await axios.get(`${BASE_URL}/connections/users`, {
      headers: {
        Authorization: `Bearer ${userTokens[userName]}`
      }
    });
    
    const users = response.data.data.list;
    console.log(`✅ 获取到 ${users.length} 个可关联用户:`);
    users.forEach(user => {
      console.log(`   - ${user.name} (${user.phone}) - 状态: ${user.connectionStatus}`);
    });
    
    return users;
  } catch (error) {
    console.error(`❌ 获取用户列表失败:`, error.response?.data?.message || error.message);
    return [];
  }
}

/**
 * 发送关联申请
 */
async function sendConnectionRequest(senderName, receiverName) {
  console.log(`\n📤 ${senderName} 向 ${receiverName} 发送关联申请...`);
  
  try {
    const response = await axios.post(`${BASE_URL}/connections/request`, {
      receiverId: userIds[receiverName],
      message: `${senderName} 请求添加您为关联用户`
    }, {
      headers: {
        Authorization: `Bearer ${userTokens[senderName]}`
      }
    });
    
    console.log(`✅ 关联申请发送成功`);
    return response.data.data;
  } catch (error) {
    console.error(`❌ 发送关联申请失败:`, error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 获取我的关联列表
 */
async function getMyConnections(userName, status = 'accepted') {
  console.log(`\n📋 ${userName} 获取关联列表 (状态: ${status})...`);
  
  try {
    const response = await axios.get(`${BASE_URL}/connections/my?status=${status}`, {
      headers: {
        Authorization: `Bearer ${userTokens[userName]}`
      }
    });
    
    const connections = response.data.data;
    console.log(`✅ 获取到 ${connections.length} 个关联:`);
    connections.forEach(conn => {
      const role = conn.isSender ? '发起者' : '接收者';
      console.log(`   - ${conn.connectedUser.name} (${role}) - 状态: ${conn.status}`);
    });
    
    return connections;
  } catch (error) {
    console.error(`❌ 获取关联列表失败:`, error.response?.data?.message || error.message);
    return [];
  }
}

/**
 * 处理关联申请
 */
async function respondToConnection(userName, connectionId, action) {
  console.log(`\n✅ ${userName} ${action === 'accept' ? '同意' : '拒绝'}关联申请...`);
  
  try {
    const response = await axios.put(`${BASE_URL}/connections/${connectionId}/respond`, {
      action
    }, {
      headers: {
        Authorization: `Bearer ${userTokens[userName]}`
      }
    });
    
    console.log(`✅ 关联申请处理成功`);
    return response.data.data;
  } catch (error) {
    console.error(`❌ 处理关联申请失败:`, error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 创建菜单并测试推送
 */
async function createMenuAndTestPush(userName) {
  console.log(`\n🍽️  ${userName} 创建今日菜单并测试推送...`);
  
  try {
    // 先获取一些菜品
    const dishResponse = await axios.get(`${BASE_URL}/dishes`, {
      headers: {
        Authorization: `Bearer ${userTokens[userName]}`
      }
    });
    
    const dishes = dishResponse.data.data.list || [];
    if (dishes.length === 0) {
      console.log('⚠️  没有可用菜品，跳过菜单创建');
      return;
    }
    
    // 创建今日菜单
    const menuData = {
      date: new Date().toISOString(),
      isToday: true,
      remark: '今日特色菜单',
      dishes: dishes.slice(0, 3).map(dish => ({
        id: dish.id,
        count: 1
      }))
    };
    
    const response = await axios.post(`${BASE_URL}/menus`, menuData, {
      headers: {
        Authorization: `Bearer ${userTokens[userName]}`
      }
    });
    
    console.log(`✅ 菜单创建成功，已推送给关联用户`);
    return response.data.data;
  } catch (error) {
    console.error(`❌ 创建菜单失败:`, error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 主测试流程
 */
async function runTests() {
  console.log('🚀 开始用户关联功能测试\n');
  
  try {
    // 1. 注册用户
    await registerUsers();
    
    // 2. 用户登录
    await loginUsers();
    
    // 3. 张三查看可关联用户
    await getAvailableUsers('张三');
    
    // 4. 张三向李四发送关联申请
    const connection1 = await sendConnectionRequest('张三', '李四');
    
    // 5. 张三向王五发送关联申请
    const connection2 = await sendConnectionRequest('张三', '王五');
    
    // 6. 李四查看待处理申请
    const pendingConnections = await getMyConnections('李四', 'pending');
    
    // 7. 李四同意张三的申请
    if (pendingConnections.length > 0) {
      await respondToConnection('李四', pendingConnections[0].id, 'accept');
    }
    
    // 8. 王五查看待处理申请并同意
    const pendingConnections2 = await getMyConnections('王五', 'pending');
    if (pendingConnections2.length > 0) {
      await respondToConnection('王五', pendingConnections2[0].id, 'accept');
    }
    
    // 9. 查看最终的关联关系
    await getMyConnections('张三', 'accepted');
    await getMyConnections('李四', 'accepted');
    await getMyConnections('王五', 'accepted');
    
    // 10. 王五创建菜单并测试推送
    await createMenuAndTestPush('王五');
    
    console.log('\n🎉 用户关联功能测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
runTests().catch(console.error);
