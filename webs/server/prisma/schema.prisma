// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider     = "postgresql"
  url          = env("DATABASE_URL")
  // relationMode = "prisma"
    // uncomment next line if you use Prisma <5.10
  // directUrl = env("DATABASE_URL_UNPOOLED")
}

model User {
  id            String         @id @default(cuid())
  name          String
  phone         String?        @unique
  password      String?
  avatar        String?
  openid        String?        @unique
  role          String         @default("user")
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  orders        Order[]
  messages      Message[]
  notifications Notification[]
  sentNotifications Notification[] @relation("NotificationSender")

  // 用户关联关系 - 发起的关联
  sentConnections UserConnection[] @relation("ConnectionSender")
  // 用户关联关系 - 接收的关联
  receivedConnections UserConnection[] @relation("ConnectionReceiver")
  // 用户分组 - 创建的分组
  ownedGroups UserGroup[]
}

model Dish {
  id          String     @id @default(cuid())
  name        String
  description String?
  image       String?
  categoryId  String
  category    Category   @relation(fields: [categoryId], references: [id])
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  menuItems   MenuItem[]

  @@index([categoryId])
}

model Category {
  id        String   @id @default(cuid())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  dishes    Dish[]
}

model Menu {
  id        String     @id @default(cuid())
  date      DateTime
  isToday   Boolean    @default(false)
  remark    String?
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  items     MenuItem[]
}

model MenuItem {
  id        String   @id @default(cuid())
  menuId    String
  dishId    String
  count     Int
  menu      Menu     @relation(fields: [menuId], references: [id], onDelete: Cascade)
  dish      Dish     @relation(fields: [dishId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([menuId])
  @@index([dishId])
}

model Order {
  id         String    @id @default(cuid())
  userId     String
  user       User      @relation(fields: [userId], references: [id])
  items      Json      // 存储订单项的 JSON 数据
  remark     String?
  diningTime DateTime?
  status     String    @default("pending")
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  @@index([userId])
}

model Message {
  id        String   @id @default(cuid())
  content   String
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  read      Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
}

model Notification {
  id        String   @id @default(cuid())
  title     String   @default("新通知")
  content   String
  type      String   @default("general") // 通知类型：general, family, admin, new_dish, new_order, menu_push, etc.
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  senderId  String?
  sender    User?    @relation("NotificationSender", fields: [senderId], references: [id])
  data      String?  // JSON 格式的额外数据
  read      Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@index([senderId])
  @@index([type])
  @@index([read])
}

model UserConnection {
  id          String   @id @default(cuid())
  senderId    String   // 发起关联的用户ID
  receiverId  String   // 接收关联的用户ID
  status      String   @default("pending") // pending, accepted, rejected
  message     String?  // 申请消息
  remark      String?  // 关联备注
  groupId     String?  // 所属分组ID
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  sender      User     @relation("ConnectionSender", fields: [senderId], references: [id], onDelete: Cascade)
  receiver    User     @relation("ConnectionReceiver", fields: [receiverId], references: [id], onDelete: Cascade)
  group       UserGroup? @relation(fields: [groupId], references: [id], onDelete: SetNull)

  @@unique([senderId, receiverId]) // 防止重复关联
  @@index([senderId])
  @@index([receiverId])
  @@index([status])
  @@index([groupId])
}

model UserGroup {
  id          String   @id @default(cuid())
  name        String   // 分组名称
  description String?  // 分组描述
  color       String?  // 分组颜色
  ownerId     String   // 分组创建者ID
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  owner       User     @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  connections UserConnection[]

  @@index([ownerId])
}
