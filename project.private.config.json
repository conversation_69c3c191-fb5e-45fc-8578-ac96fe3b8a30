{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "nanan", "setting": {"compileHotReLoad": true, "urlCheck": false, "bigPackageSizeSupport": true, "autoAudits": false, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "checkInvalidKey": true, "ignoreDevUnusedFiles": true}, "libVersion": "3.7.12", "condition": {}, "ignore": ["webs/admin/**/*"]}