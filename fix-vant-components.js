const fs = require('fs');
const path = require('path');

/**
 * 修复Vant组件路径问题的脚本
 * 将所有页面的Vant组件路径统一修改为正确的路径
 */

// 需要修复的页面目录
const pageDirs = [
  'pages/user_connection',
  'pages/mine',
  'pages/home',
  'pages/order',
  'pages/add_menu',
  'pages/statistics',
  'pages/today_order',
  'pages/login',
  'pages/message',
  'pages/family_message',
  'pages/notification_center',
  'pages/history_menu',
  'pages/detail'
];

// Vant组件映射 - 从错误路径到正确路径
const vantComponentMap = {
  '@vant/weapp/tabs/index': '/miniprogram_npm/@vant/weapp/tabs/index',
  '@vant/weapp/tab/index': '/miniprogram_npm/@vant/weapp/tab/index',
  '@vant/weapp/icon/index': '/miniprogram_npm/@vant/weapp/icon/index',
  '@vant/weapp/button/index': '/miniprogram_npm/@vant/weapp/button/index',
  '@vant/weapp/tag/index': '/miniprogram_npm/@vant/weapp/tag/index',
  '@vant/weapp/search/index': '/miniprogram_npm/@vant/weapp/search/index',
  '@vant/weapp/dialog/index': '/miniprogram_npm/@vant/weapp/dialog/index',
  '@vant/weapp/field/index': '/miniprogram_npm/@vant/weapp/field/index',
  '@vant/weapp/loading/index': '/miniprogram_npm/@vant/weapp/loading/index',
  '@vant/weapp/cell/index': '/miniprogram_npm/@vant/weapp/cell/index',
  '@vant/weapp/cell-group/index': '/miniprogram_npm/@vant/weapp/cell-group/index',
  '@vant/weapp/popup/index': '/miniprogram_npm/@vant/weapp/popup/index',
  '@vant/weapp/picker/index': '/miniprogram_npm/@vant/weapp/picker/index',
  '@vant/weapp/datetime-picker/index': '/miniprogram_npm/@vant/weapp/datetime-picker/index',
  '@vant/weapp/action-sheet/index': '/miniprogram_npm/@vant/weapp/action-sheet/index',
  '@vant/weapp/swipe-cell/index': '/miniprogram_npm/@vant/weapp/swipe-cell/index',
  '@vant/weapp/empty/index': '/miniprogram_npm/@vant/weapp/empty/index',
  '@vant/weapp/divider/index': '/miniprogram_npm/@vant/weapp/divider/index',
  '@vant/weapp/toast/index': '/miniprogram_npm/@vant/weapp/toast/index',
  '@vant/weapp/notify/index': '/miniprogram_npm/@vant/weapp/notify/index',
  '@vant/weapp/overlay/index': '/miniprogram_npm/@vant/weapp/overlay/index'
};

/**
 * 修复单个JSON文件中的Vant组件路径
 */
function fixVantPaths(jsonFilePath) {
  try {
    if (!fs.existsSync(jsonFilePath)) {
      console.log(`⚠️  文件不存在: ${jsonFilePath}`);
      return false;
    }

    const content = fs.readFileSync(jsonFilePath, 'utf8');
    let jsonData;
    
    try {
      jsonData = JSON.parse(content);
    } catch (parseError) {
      console.log(`❌ JSON解析失败: ${jsonFilePath}`);
      return false;
    }

    if (!jsonData.usingComponents) {
      console.log(`ℹ️  无需修复: ${jsonFilePath} (无usingComponents)`);
      return false;
    }

    let hasChanges = false;
    const usingComponents = jsonData.usingComponents;

    // 修复Vant组件路径
    for (const [componentName, componentPath] of Object.entries(usingComponents)) {
      if (vantComponentMap[componentPath]) {
        usingComponents[componentName] = vantComponentMap[componentPath];
        hasChanges = true;
        console.log(`🔧 修复组件: ${componentName} -> ${vantComponentMap[componentPath]}`);
      }
    }

    if (hasChanges) {
      fs.writeFileSync(jsonFilePath, JSON.stringify(jsonData, null, 2), 'utf8');
      console.log(`✅ 修复完成: ${jsonFilePath}`);
      return true;
    } else {
      console.log(`ℹ️  无需修复: ${jsonFilePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 修复失败: ${jsonFilePath}`, error.message);
    return false;
  }
}

/**
 * 修复app.json中的全局组件路径
 */
function fixAppJson() {
  const appJsonPath = 'app.json';
  console.log('\n🔧 修复全局组件配置...');
  
  try {
    const content = fs.readFileSync(appJsonPath, 'utf8');
    const jsonData = JSON.parse(content);
    
    if (!jsonData.usingComponents) {
      console.log('ℹ️  app.json 无全局组件配置');
      return;
    }

    let hasChanges = false;
    const usingComponents = jsonData.usingComponents;

    for (const [componentName, componentPath] of Object.entries(usingComponents)) {
      if (vantComponentMap[componentPath]) {
        usingComponents[componentName] = vantComponentMap[componentPath];
        hasChanges = true;
        console.log(`🔧 修复全局组件: ${componentName} -> ${vantComponentMap[componentPath]}`);
      }
    }

    if (hasChanges) {
      fs.writeFileSync(appJsonPath, JSON.stringify(jsonData, null, 2), 'utf8');
      console.log('✅ app.json 修复完成');
    } else {
      console.log('ℹ️  app.json 无需修复');
    }
  } catch (error) {
    console.error('❌ 修复app.json失败:', error.message);
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始修复Vant组件路径问题...\n');

  let totalFixed = 0;

  // 修复app.json
  fixAppJson();

  // 修复各个页面的JSON文件
  console.log('\n🔧 修复页面组件配置...');
  for (const pageDir of pageDirs) {
    const jsonFilePath = path.join(pageDir, 'index.json');
    console.log(`\n📁 处理: ${pageDir}`);
    
    if (fixVantPaths(jsonFilePath)) {
      totalFixed++;
    }
  }

  console.log(`\n🎉 修复完成！共修复了 ${totalFixed} 个文件`);
  console.log('\n📝 修复说明:');
  console.log('- 将 @vant/weapp/* 路径修改为 /miniprogram_npm/@vant/weapp/*');
  console.log('- 这样可以避免构建npm时的路径问题');
  console.log('- 现在可以直接在微信开发者工具中使用');
}

// 运行脚本
main();
