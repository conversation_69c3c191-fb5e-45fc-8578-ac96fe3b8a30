<view class="container">
  <!-- 顶部标题 -->
  <view class="header">
    <view class="title">分组管理</view>
    <view class="subtitle">管理您的用户分组</view>
  </view>

  <!-- 创建分组按钮 -->
  <view class="create-section">
    <van-button 
      type="primary" 
      size="large" 
      icon="plus" 
      bind:click="showCreateDialog"
      block
    >
      创建新分组
    </van-button>
  </view>

  <!-- 分组列表 -->
  <view wx:if="{{groups.length > 0}}" class="group-list">
    <view 
      wx:for="{{groups}}" 
      wx:key="id" 
      class="group-item"
      bind:tap="goToGroupDetail"
      data-group="{{item}}"
    >
      <view class="group-info">
        <view class="group-header">
          <view class="group-color" style="background-color: {{item.color}}"></view>
          <view class="group-name">{{item.name}}</view>
          <view class="member-count">{{item.memberCount}}人</view>
        </view>
        <view wx:if="{{item.description}}" class="group-description">
          {{item.description}}
        </view>
        <view class="group-time">
          创建于 {{item.createdAt}}
        </view>
      </view>
      <view class="group-actions">
        <van-button 
          size="small" 
          type="default" 
          bind:click="editGroup"
          data-group="{{item}}"
          catch:tap="stopPropagation"
        >
          编辑
        </van-button>
        <van-button 
          size="small" 
          type="danger" 
          plain
          bind:click="deleteGroup"
          data-id="{{item.id}}"
          data-name="{{item.name}}"
          catch:tap="stopPropagation"
          style="margin-left: 16rpx;"
        >
          删除
        </van-button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:else class="empty-state">
    <van-icon name="apps-o" size="80rpx" color="#D1D5DB" />
    <view class="empty-text">暂无分组</view>
    <view class="empty-desc">创建分组来更好地管理您的关联用户</view>
  </view>
</view>

<!-- 创建/编辑分组弹窗 -->
<van-dialog
  use-slot
  title="{{isEditing ? '编辑分组' : '创建分组'}}"
  show="{{ showGroupDialog }}"
  show-cancel-button
  bind:confirm="confirmGroupAction"
  bind:cancel="cancelGroupAction"
>
  <view class="dialog-content">
    <van-field
      value="{{ groupForm.name }}"
      label="分组名称"
      placeholder="请输入分组名称"
      required
      maxlength="20"
      show-word-limit
      bind:change="onGroupNameChange"
    />
    
    <van-field
      value="{{ groupForm.description }}"
      label="分组描述"
      placeholder="请输入分组描述（可选）"
      type="textarea"
      maxlength="100"
      show-word-limit
      bind:change="onGroupDescriptionChange"
    />
    
    <view class="color-section">
      <view class="color-label">分组颜色</view>
      <view class="color-options">
        <view 
          wx:for="{{colorOptions}}" 
          wx:key="*this"
          class="color-option {{groupForm.color === item ? 'selected' : ''}}"
          style="background-color: {{item}}"
          bind:tap="selectColor"
          data-color="{{item}}"
        ></view>
      </view>
    </view>
  </view>
</van-dialog>

<!-- 加载状态 -->
<van-loading wx:if="{{loading}}" type="spinner" color="#6366F1" />

<!-- Toast提示 -->
<van-toast id="van-toast" />
