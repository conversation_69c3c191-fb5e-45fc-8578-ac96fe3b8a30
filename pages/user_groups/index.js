const {userGroupApi} = require('../../services/api');
import Toast from '/miniprogram_npm/@vant/weapp/toast/toast';

Page({
  data: {
    loading: false,
    groups: [],
    
    // 分组弹窗
    showGroupDialog: false,
    isEditing: false,
    editingGroupId: null,
    groupForm: {
      name: '',
      description: '',
      color: '#6366F1'
    },
    
    // 颜色选项
    colorOptions: [
      '#6366F1', '#8B5CF6', '#EC4899', '#EF4444',
      '#F59E0B', '#10B981', '#06B6D4', '#6B7280'
    ]
  },

  onLoad() {
    this.loadGroups();
  },

  onShow() {
    this.loadGroups();
  },

  /**
   * 加载分组列表
   */
  async loadGroups() {
    try {
      this.setData({loading: true});
      const res = await userGroupApi.getMyGroups();
      
      if (res.code === 200) {
        this.setData({
          groups: (res.data || []).map(group => ({
            ...group,
            createdAt: this.formatTime(group.createdAt)
          }))
        });
      } else {
        Toast.fail(res.message || '加载失败');
      }
    } catch (error) {
      console.error('Load groups error:', error);
      Toast.fail('网络错误');
    } finally {
      this.setData({loading: false});
    }
  },

  /**
   * 显示创建分组弹窗
   */
  showCreateDialog() {
    this.setData({
      showGroupDialog: true,
      isEditing: false,
      editingGroupId: null,
      groupForm: {
        name: '',
        description: '',
        color: '#6366F1'
      }
    });
  },

  /**
   * 编辑分组
   */
  editGroup(event) {
    const {group} = event.currentTarget.dataset;
    this.setData({
      showGroupDialog: true,
      isEditing: true,
      editingGroupId: group.id,
      groupForm: {
        name: group.name,
        description: group.description || '',
        color: group.color || '#6366F1'
      }
    });
  },

  /**
   * 删除分组
   */
  deleteGroup(event) {
    const {id, name} = event.currentTarget.dataset;
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除分组"${name}"吗？删除后该分组下的关联用户将移出分组。`,
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await userGroupApi.deleteGroup(id);
            
            if (result.code === 200) {
              Toast.success('删除成功');
              this.loadGroups();
            } else {
              Toast.fail(result.message || '删除失败');
            }
          } catch (error) {
            console.error('Delete group error:', error);
            Toast.fail('网络错误');
          }
        }
      }
    });
  },

  /**
   * 进入分组详情页
   */
  goToGroupDetail(event) {
    const {group} = event.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/group_detail/index?groupId=${group.id}&groupName=${group.name}`
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 分组名称输入
   */
  onGroupNameChange(event) {
    this.setData({
      'groupForm.name': event.detail
    });
  },

  /**
   * 分组描述输入
   */
  onGroupDescriptionChange(event) {
    this.setData({
      'groupForm.description': event.detail
    });
  },

  /**
   * 选择颜色
   */
  selectColor(event) {
    const {color} = event.currentTarget.dataset;
    this.setData({
      'groupForm.color': color
    });
  },

  /**
   * 确认分组操作
   */
  async confirmGroupAction() {
    const {groupForm, isEditing, editingGroupId} = this.data;
    
    if (!groupForm.name.trim()) {
      Toast.fail('请输入分组名称');
      return;
    }

    try {
      let res;
      if (isEditing) {
        res = await userGroupApi.updateGroup(editingGroupId, groupForm);
      } else {
        res = await userGroupApi.createGroup(groupForm);
      }
      
      if (res.code === 200 || res.code === 201) {
        Toast.success(isEditing ? '更新成功' : '创建成功');
        this.setData({showGroupDialog: false});
        this.loadGroups();
      } else {
        Toast.fail(res.message || '操作失败');
      }
    } catch (error) {
      console.error('Group action error:', error);
      Toast.fail('网络错误');
    }
  },

  /**
   * 取消分组操作
   */
  cancelGroupAction() {
    this.setData({
      showGroupDialog: false,
      groupForm: {
        name: '',
        description: '',
        color: '#6366F1'
      }
    });
  },

  /**
   * 格式化时间
   */
  formatTime(timeString) {
    const date = new Date(timeString);
    return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
  }
});
