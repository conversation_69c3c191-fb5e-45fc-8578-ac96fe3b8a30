.container {
  padding: 0;
  background-color: #f8fafc;
  min-height: 100vh;
}

.header {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  padding: 40rpx 32rpx 32rpx;
  color: white;
  
  .title {
    font-size: 48rpx;
    font-weight: bold;
    margin-bottom: 8rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    opacity: 0.9;
  }
}

.create-section {
  padding: 32rpx;
}

.group-list {
  padding: 0 32rpx 32rpx;
  
  .group-item {
    background: white;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    
    .group-info {
      flex: 1;
      
      .group-header {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;
        
        .group-color {
          width: 24rpx;
          height: 24rpx;
          border-radius: 50%;
          margin-right: 16rpx;
        }
        
        .group-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #1f2937;
          flex: 1;
        }
        
        .member-count {
          font-size: 24rpx;
          color: #6b7280;
          background-color: #f3f4f6;
          padding: 8rpx 16rpx;
          border-radius: 12rpx;
        }
      }
      
      .group-description {
        font-size: 28rpx;
        color: #6b7280;
        margin-bottom: 12rpx;
        line-height: 1.4;
      }
      
      .group-time {
        font-size: 24rpx;
        color: #9ca3af;
      }
    }
    
    .group-actions {
      display: flex;
      align-items: center;
      gap: 16rpx;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  text-align: center;
  
  .empty-text {
    font-size: 32rpx;
    color: #6b7280;
    margin: 24rpx 0 16rpx;
    font-weight: 500;
  }
  
  .empty-desc {
    font-size: 28rpx;
    color: #9ca3af;
    line-height: 1.5;
  }
}

.dialog-content {
  padding: 32rpx;
  
  .color-section {
    margin-top: 32rpx;
    
    .color-label {
      font-size: 28rpx;
      color: #374151;
      margin-bottom: 16rpx;
    }
    
    .color-options {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
      
      .color-option {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        border: 4rpx solid transparent;
        cursor: pointer;
        
        &.selected {
          border-color: #1f2937;
          transform: scale(1.1);
        }
      }
    }
  }
}

.van-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

.van-button--primary {
  background-color: #6366f1;
  border-color: #6366f1;
}

.van-button--danger {
  color: #ef4444;
  border-color: #ef4444;
}

.van-button--danger.van-button--plain {
  background-color: transparent;
}
