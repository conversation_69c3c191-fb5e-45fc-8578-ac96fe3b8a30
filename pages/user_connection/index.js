const {connectionApi, userGroupApi} = require('../../services/api');

Page({
  data: {
    activeTab: 'my',
    loading: false,

    // 我的关联
    myConnections: [],

    // 可关联用户
    availableUsers: [],
    searchKeyword: '',

    // 待处理申请
    pendingRequests: [],

    // 发送申请弹窗
    showRequestDialog: false,
    selectedUser: {},
    requestMessage: '',

    // 编辑关联弹窗
    showEditDialog: false,
    editingConnection: {},
    editForm: {
      remark: '',
      groupId: '',
      groupName: ''
    },

    // 分组相关
    userGroups: [],
    showGroupPicker: false
  },

  onLoad() {
    this.loadMyConnections();
    this.loadAvailableUsers();
    this.loadPendingRequests();
    this.loadUserGroups();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadMyConnections();
    this.loadPendingRequests();
  },

  /**
   * 标签页切换
   */
  onTabChange(event) {
    const {name} = event.detail;
    this.setData({activeTab: name});

    // 根据标签页加载对应数据
    if (name === 'my') {
      this.loadMyConnections();
    } else if (name === 'add') {
      this.loadAvailableUsers();
    } else if (name === 'requests') {
      this.loadPendingRequests();
    }
  },

  /**
   * 加载我的关联列表
   */
  async loadMyConnections() {
    try {
      this.setData({loading: true});
      const res = await connectionApi.getMyConnections('accepted');

      if (res.code === 200) {
        this.setData({
          myConnections: res.data || []
        });
      } else {
        wx.showToast({
          title: res.message || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('Load my connections error:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    } finally {
      this.setData({loading: false});
    }
  },

  /**
   * 加载可关联用户列表
   */
  async loadAvailableUsers(search = '') {
    try {
      this.setData({loading: true});
      const params = {
        page: 1,
        size: 50
      };

      if (search) {
        params.search = search;
      }

      const res = await connectionApi.getAvailableUsers(params);

      if (res.code === 200) {
        this.setData({
          availableUsers: res.data.list || []
        });
      } else {
        wx.showToast({
          title: res.message || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('Load available users error:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    } finally {
      this.setData({loading: false});
    }
  },

  /**
   * 加载待处理申请
   */
  async loadPendingRequests() {
    try {
      this.setData({loading: true});
      const res = await connectionApi.getMyConnections('pending');

      if (res.code === 200) {
        // 只显示我作为接收者的申请
        const pendingRequests = (res.data || []).filter(item => !item.isSender);
        this.setData({
          pendingRequests: pendingRequests.map(item => ({
            ...item,
            createdAt: this.formatTime(item.createdAt)
          }))
        });
      } else {
        wx.showToast({
          title: res.message || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('Load pending requests error:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    } finally {
      this.setData({loading: false});
    }
  },

  /**
   * 搜索用户
   */
  onSearch(event) {
    const keyword = event.detail;
    this.setData({searchKeyword: keyword});
    this.loadAvailableUsers(keyword);
  },

  onSearchChange(event) {
    this.setData({searchKeyword: event.detail});
  },

  onSearchClear() {
    this.setData({searchKeyword: ''});
    this.loadAvailableUsers();
  },

  /**
   * 发送关联申请
   */
  sendConnectionRequest(event) {
    const {id, name} = event.currentTarget.dataset;
    this.setData({
      selectedUser: {id, name},
      showRequestDialog: true,
      requestMessage: `请求添加为关联用户`
    });
  },

  onRequestMessageChange(event) {
    this.setData({requestMessage: event.detail});
  },

  async confirmSendRequest() {
    try {
      const {selectedUser, requestMessage} = this.data;

      const res = await connectionApi.sendConnectionRequest({
        receiverId: selectedUser.id,
        message: requestMessage
      });

      if (res.code === 201) {
        wx.showToast({
          title: '申请已发送',
          icon: 'success'
        });

        // 刷新用户列表
        this.loadAvailableUsers(this.data.searchKeyword);
      } else {
        wx.showToast({
          title: res.message || '发送失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('Send connection request error:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    } finally {
      this.setData({
        showRequestDialog: false,
        selectedUser: {},
        requestMessage: ''
      });
    }
  },

  cancelSendRequest() {
    this.setData({
      showRequestDialog: false,
      selectedUser: {},
      requestMessage: ''
    });
  },

  /**
   * 处理关联申请
   */
  async respondToRequest(event) {
    const {id, action} = event.currentTarget.dataset;

    try {
      const res = await connectionApi.respondToConnection(id, action);

      if (res.code === 200) {
        wx.showToast({
          title: action === 'accept' ? '已同意申请' : '已拒绝申请',
          icon: 'success'
        });

        // 刷新数据
        this.loadPendingRequests();
        this.loadMyConnections();
      } else {
        wx.showToast({
          title: res.message || '操作失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('Respond to request error:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    }
  },

  /**
   * 解除关联
   */
  removeConnection(event) {
    const {id} = event.currentTarget.dataset;

    wx.showModal({
      title: '确认解除',
      content: '确定要解除与该用户的关联关系吗？',
      success: async res => {
        if (res.confirm) {
          try {
            const result = await connectionApi.removeConnection(id);

            if (result.code === 200) {
              wx.showToast({
                title: '已解除关联',
                icon: 'success'
              });

              // 刷新列表
              this.loadMyConnections();
            } else {
              wx.showToast({
                title: result.message || '操作失败',
                icon: 'none'
              });
            }
          } catch (error) {
            console.error('Remove connection error:', error);
            wx.showToast({
              title: '网络错误',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 加载用户分组
   */
  async loadUserGroups() {
    try {
      const res = await userGroupApi.getMyGroups();
      if (res.code === 200) {
        this.setData({
          userGroups: res.data || []
        });
      }
    } catch (error) {
      console.error('Load user groups error:', error);
    }
  },

  /**
   * 进入分组管理页面
   */
  goToGroupManage() {
    wx.navigateTo({
      url: '/pages/user_groups/index'
    });
  },

  /**
   * 编辑关联
   */
  editConnection(event) {
    const {connection} = event.currentTarget.dataset;
    this.setData({
      showEditDialog: true,
      editingConnection: connection,
      editForm: {
        remark: connection.remark || '',
        groupId: connection.group?.id || '',
        groupName: connection.group?.name || ''
      }
    });
  },

  /**
   * 编辑备注输入
   */
  onEditRemarkChange(event) {
    this.setData({
      'editForm.remark': event.detail
    });
  },

  /**
   * 显示分组选择器
   */
  showGroupPicker() {
    this.setData({showGroupPicker: true});
  },

  /**
   * 关闭分组选择器
   */
  closeGroupPicker() {
    this.setData({showGroupPicker: false});
  },

  /**
   * 选择分组
   */
  selectGroup(event) {
    const {groupId, groupName} = event.currentTarget.dataset;
    this.setData({
      'editForm.groupId': groupId,
      'editForm.groupName': groupName,
      showGroupPicker: false
    });
  },

  /**
   * 确认编辑关联
   */
  async confirmEditConnection() {
    try {
      const {editingConnection, editForm} = this.data;

      const res = await connectionApi.updateConnection(editingConnection.id, {
        remark: editForm.remark,
        groupId: editForm.groupId || null
      });

      if (res.code === 200) {
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        });

        this.setData({showEditDialog: false});
        this.loadMyConnections();
      } else {
        wx.showToast({
          title: res.message || '更新失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('Update connection error:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    }
  },

  /**
   * 取消编辑关联
   */
  cancelEditConnection() {
    this.setData({
      showEditDialog: false,
      editingConnection: {},
      editForm: {
        remark: '',
        groupId: '',
        groupName: ''
      }
    });
  },

  /**
   * 格式化时间
   */
  formatTime(timeString) {
    const date = new Date(timeString);
    const now = new Date();
    const diff = now - date;

    if (diff < 60000) {
      // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) {
      // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) {
      // 1天内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    }
  }
});
