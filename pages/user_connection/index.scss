.container {
  padding: 0;
  background-color: #f8fafc;
  min-height: 100vh;
}

.header {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  padding: 40rpx 32rpx 32rpx;
  color: white;
  
  .title {
    font-size: 48rpx;
    font-weight: bold;
    margin-bottom: 8rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    opacity: 0.9;
  }
}

.tab-content {
  padding: 32rpx;
}

/* 搜索区域 */
.search-section {
  margin-bottom: 32rpx;
}

/* 连接列表样式 */
.connection-list,
.user-list,
.request-list {
  .connection-item,
  .user-item,
  .request-item {
    background: white;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    
    .user-info {
      display: flex;
      align-items: center;
      flex: 1;
      
      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        background-color: #f3f4f6;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;
        overflow: hidden;
        
        image {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
      
      .info {
        flex: 1;
        
        .name {
          font-size: 32rpx;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 8rpx;
        }
        
        .phone {
          font-size: 28rpx;
          color: #6b7280;
          margin-bottom: 4rpx;
        }
        
        .role {
          font-size: 24rpx;
          color: #9ca3af;
        }
        
        .message {
          font-size: 28rpx;
          color: #4b5563;
          margin-bottom: 8rpx;
          background-color: #f9fafb;
          padding: 16rpx;
          border-radius: 8rpx;
          margin-top: 8rpx;
        }
        
        .time {
          font-size: 24rpx;
          color: #9ca3af;
        }
      }
    }
    
    .actions {
      display: flex;
      align-items: center;
      gap: 16rpx;
    }
  }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  text-align: center;
  
  .empty-text {
    font-size: 32rpx;
    color: #6b7280;
    margin: 24rpx 0 16rpx;
    font-weight: 500;
  }
  
  .empty-desc {
    font-size: 28rpx;
    color: #9ca3af;
    line-height: 1.5;
  }
}

/* 弹窗内容 */
.dialog-content {
  padding: 32rpx;
  
  .dialog-text {
    font-size: 32rpx;
    color: #374151;
    margin-bottom: 32rpx;
    text-align: center;
  }
}

/* 加载状态 */
.van-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

/* 标签页样式调整 */
.van-tabs {
  background: white;
}

.van-tabs__nav {
  background: white;
  border-bottom: 1rpx solid #e5e7eb;
}

.van-tab {
  font-size: 30rpx;
}

.van-tab--active {
  color: #6366f1;
  font-weight: 600;
}

.van-tabs__line {
  background-color: #6366f1;
}

/* 按钮样式调整 */
.van-button--primary {
  background-color: #6366f1;
  border-color: #6366f1;
}

.van-button--danger {
  color: #ef4444;
  border-color: #ef4444;
}

.van-button--danger.van-button--plain {
  background-color: transparent;
}

/* 标签样式 */
.van-tag--warning {
  background-color: #fef3c7;
  color: #d97706;
}

.van-tag--success {
  background-color: #d1fae5;
  color: #059669;
}

.van-tag--danger {
  background-color: #fee2e2;
  color: #dc2626;
}
