<view class="container">
  <!-- 顶部信息 -->
  <view class="header">
    <view class="group-info">
      <view class="group-header">
        <view class="group-color" style="background-color: {{groupData.color}}"></view>
        <view class="group-name">{{groupData.name}}</view>
      </view>
      <view wx:if="{{groupData.description}}" class="group-description">
        {{groupData.description}}
      </view>
      <view class="member-count">{{groupData.memberCount}}位成员</view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <van-button 
      type="primary" 
      size="small" 
      icon="plus" 
      bind:click="addMembersToGroup"
    >
      添加成员
    </van-button>
    <van-button 
      type="default" 
      size="small" 
      icon="edit" 
      bind:click="editGroup"
      style="margin-left: 16rpx;"
    >
      编辑分组
    </van-button>
  </view>

  <!-- 成员列表 -->
  <view class="members-section">
    <view class="section-title">分组成员</view>
    
    <view wx:if="{{groupData.members && groupData.members.length > 0}}" class="member-list">
      <view 
        wx:for="{{groupData.members}}" 
        wx:key="connectionId" 
        class="member-item"
      >
        <view class="member-info">
          <view class="avatar">
            <image 
              wx:if="{{item.user.avatar}}" 
              src="{{item.user.avatar}}" 
              mode="aspectFill"
            />
            <van-icon wx:else name="user-o" size="40rpx" color="#6366F1" />
          </view>
          <view class="info">
            <view class="name">{{item.user.name}}</view>
            <view wx:if="{{item.remark}}" class="remark">{{item.remark}}</view>
            <view class="phone" wx:if="{{item.user.phone}}">{{item.user.phone}}</view>
            <view class="join-time">加入时间：{{item.joinedAt}}</view>
          </view>
        </view>
        <view class="member-actions">
          <van-button 
            size="mini" 
            type="default" 
            bind:click="editMemberRemark"
            data-connection="{{item}}"
          >
            编辑备注
          </van-button>
          <van-button 
            size="mini" 
            type="danger" 
            plain
            bind:click="removeMemberFromGroup"
            data-connection-id="{{item.connectionId}}"
            data-name="{{item.user.name}}"
            style="margin-left: 16rpx;"
          >
            移出分组
          </van-button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:else class="empty-state">
      <van-icon name="friends-o" size="80rpx" color="#D1D5DB" />
      <view class="empty-text">暂无成员</view>
      <view class="empty-desc">点击"添加成员"将关联用户加入此分组</view>
    </view>
  </view>
</view>

<!-- 编辑备注弹窗 -->
<van-dialog
  use-slot
  title="编辑备注"
  show="{{ showRemarkDialog }}"
  show-cancel-button
  bind:confirm="confirmEditRemark"
  bind:cancel="cancelEditRemark"
>
  <view class="dialog-content">
    <view class="dialog-text">为 {{editingMember.user.name}} 设置备注</view>
    <van-field
      value="{{ remarkText }}"
      placeholder="请输入备注信息（可选）"
      maxlength="50"
      show-word-limit
      bind:change="onRemarkChange"
    />
  </view>
</van-dialog>

<!-- 添加成员弹窗 -->
<van-popup
  show="{{ showAddMemberPopup }}"
  position="bottom"
  round
  bind:close="closeAddMemberPopup"
>
  <view class="popup-header">
    <view class="popup-title">添加成员到分组</view>
    <van-icon name="cross" bind:click="closeAddMemberPopup" />
  </view>
  
  <view class="popup-content">
    <!-- 搜索框 -->
    <van-search
      value="{{ searchKeyword }}"
      placeholder="搜索关联用户"
      bind:search="searchConnections"
      bind:change="onSearchChange"
    />
    
    <!-- 可添加的关联用户列表 -->
    <view wx:if="{{availableConnections.length > 0}}" class="connection-list">
      <view 
        wx:for="{{availableConnections}}" 
        wx:key="id" 
        class="connection-item"
      >
        <view class="user-info">
          <view class="avatar">
            <image 
              wx:if="{{item.connectedUser.avatar}}" 
              src="{{item.connectedUser.avatar}}" 
              mode="aspectFill"
            />
            <van-icon wx:else name="user-o" size="40rpx" color="#6366F1" />
          </view>
          <view class="info">
            <view class="name">{{item.connectedUser.name}}</view>
            <view wx:if="{{item.remark}}" class="remark">{{item.remark}}</view>
            <view class="phone" wx:if="{{item.connectedUser.phone}}">
              {{item.connectedUser.phone}}
            </view>
          </view>
        </view>
        <van-button 
          size="small" 
          type="primary"
          bind:click="addToGroup"
          data-connection="{{item}}"
        >
          添加
        </van-button>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view wx:else class="empty-state">
      <van-icon name="search" size="60rpx" color="#D1D5DB" />
      <view class="empty-text">暂无可添加的用户</view>
    </view>
  </view>
</van-popup>

<!-- 加载状态 -->
<van-loading wx:if="{{loading}}" type="spinner" color="#6366F1" />

<!-- Toast提示 -->
<van-toast id="van-toast" />
