const app = getApp();

Page({
  data: {
    userInfo: {},
    formattedPhone: '',
    roleText: ''
  },

  onLoad() {
    const user_info = wx.getStorageSync('userInfo');
    this.setData(
      {
        userInfo: user_info
      },
      () => {
        this.formatPhoneNumber();
        this.formatRoleText();
      }
    );
  },

  formatPhoneNumber() {
    const {phone} = this.data.userInfo;
    // 格式化手机号为 138****8888 格式
    if (!phone) return;
    const formattedPhone = phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    this.setData({
      formattedPhone
    });
  },

  formatRoleText() {
    const {role} = this.data.userInfo;
    const roleMap = {
      admin: '管理员',
      user: '普通用户',
      vip: 'VIP用户',
      member: '会员'
    };
    this.setData({
      roleText: roleMap[role] || '用户'
    });
  },

  goToUserConnection() {
    wx.navigateTo({
      url: '/pages/user_connection/index'
    });
  },

  goToFamilyMessage() {
    wx.navigateTo({
      url: '/pages/family_message/index'
    });
  },

  goToNotice() {
    wx.navigateTo({
      url: '/pages/message/index'
    });
  },

  goToNotificationCenter() {
    wx.navigateTo({
      url: '/pages/notification_center/index'
    });
  },

  onLogout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: res => {
        if (res.confirm) {
          // 清除用户信息
          app.globalData.userInfo = null;
          // 清空缓存
          const tem = wx.getStorageSync('savedAccount');
          wx.clearStorageSync();
          if (tem) {
            wx.setStorageSync('savedAccount', tem);
          }
          wx.reLaunch({
            url: '/pages/login/index'
          });
        }
      }
    });
  }
});
